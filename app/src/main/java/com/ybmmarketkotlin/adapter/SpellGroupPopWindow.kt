package com.ybmmarketkotlin.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Message
import android.text.InputType
import android.text.SpannableString
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.view.isVisible
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.google.gson.Gson
import com.ybm.app.bean.NetError
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.activity.PaymentActivity
import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.product_detail.ReportPDButtonClick
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JGTrackManager.Companion.getSuperProperty
import com.ybmmarket20.common.JGTrackManager.GlobalVariable.mJgOperationInfo
import com.ybmmarket20.common.JGTrackManager.GlobalVariable.mJgSearchRowsBean
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.jgTrackProductDetailNewBtnClick
import com.ybmmarket20.common.jgTrackProductDetailNewBtnExposure
import com.ybmmarket20.common.reportAddToCart
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.DrawableCenterTextView
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.reportBean.AddToCart
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ybmmarket20.utils.*
import com.ybmmarket20.utils.DialogUtil.DialogClickListener
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.BaseBottomPopWindow
import com.ybmmarket20.view.CSUListAdapter.CSUItemEventListener
import com.ybmmarket20.view.CSUListView
import com.ybmmarket20.viewmodel.CURRENT_PAGE_DEFAULT
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_RIGHT_NOW
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_SELECT_GOODS
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore.Companion.get
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowReport
import com.ybmmarket20.xyyreport.page.order.ChangeCartUtil.getChangeCartParams
import com.ybmmarketkotlin.utils.tagStyle
import com.ydmmarket.report.ReportManager
import com.ydmmarket.report.manager.TrackManager

/**
 * 拼团和批购包邮类型底部弹窗  mIsWholeSale区分： true批购 false是拼团
 * @constructor
 */
class SpellGroupPopWindow(
    context: Context, val rowsBean: RowsBean, actPtBean: ActPtBean,
    private var mIsWholeSale: Boolean = false, private val mIsList: Boolean = false, var mFlowData: BaseFlowData? = null,
    var jgTrackBean: JgTrackBean? = null,
    var jgPageListCommonBean: JGPageListCommonBean? = null,
        var hideSpellGroupRightNow:Boolean = false, //是否需要隐藏立即参团，去抢购按钮
) : BaseBottomPopWindow(), CSUItemEventListener {
    private val context: Context
    private var tvSpellGroupPopName: TextView? = null
    private var tvSpellGroupPopSpec: TextView? = null
    private var tvSpellGroupPopInventory: TextView? = null
    private var tvSpellGroupPopEffective: TextView? = null
    private var tvSpellGroupPopInitial: TextView? = null
    private var tvSpellGroupPopsubmit: TextView? = null
    private var clSupportSuixinpin: ConstraintLayout? = null
    private var rtvSpellGroupRightNow: RoundTextView? = null
    private var rtvSpellGroupRecommendGoods: RoundTextView? = null
    private var tvSuiXinPinBubble: TextView? = null
    private var tvNumber: TextView? = null
    private var ivNumSub: AppCompatImageView? = null
    private var ivNumAdd: AppCompatImageView? = null
    private var llDiscount: LinearLayout? = null
    private var tvTotalAmount: TextView? = null
    private var groupSpellGroupPrice: Group? = null
    private var finalSkuStartNum = 0
    private var mCartDataBean: CartDataBean? = null
    private var mPromoList: RecyclerView? = null
    private var tvSpellGroupPrice: TextView? = null
    private var mPromotionTagAdapter: PromotionTagAdapter? = null
    private var mRowsBean: RowsBean? = null
    private var tvLimited: TextView? = null
    private var tvTips: DrawableCenterTextView? = null


    private fun registerRecommendGoodsJumpType(jumpType: Int) {
        if (context is ComponentActivity) {
            val viewModel: SpellGroupRecommendGoodsViewModel = ViewModelProvider(
                get().getGlobalViewModelStore(), SavedStateViewModelFactory(
                    context.application,
                    context
                )
            ).get(SpellGroupRecommendGoodsViewModel::class.java)
            viewModel.shopCode = rowsBean.shopCode ?: ""
            viewModel.orgId = rowsBean.orgId ?: ""
            viewModel.mainGoodsSkuId = "${rowsBean.id}"
            viewModel.isThirdCompany = rowsBean.isThirdCompany
            viewModel.mainGoodsCount = ""
            viewModel.mainGoodsPId = rowsBean.pId ?: ""
            viewModel.registerJumpType(jumpType)
        }
    }

    fun setData(rowsBean: RowsBean, actPtBean: ActPtBean) {
        get().getGlobalViewModelStore().clear()
        mRowsBean = rowsBean
        setBasicInformation(rowsBean, actPtBean)
        bindNumAddOrSub(rowsBean, actPtBean)
        contentView.findViewById<View>(R.id.tv_spell_group_pop_down)
            .setOnClickListener { dismiss() }
//        if (mIsList) {
//            llDiscount?.visibility = View.GONE
//            priceGroup?.visibility = View.GONE
//        }
        tvSpellGroupPopsubmit?.setOnClickListener {
            AddCartPopupWindowReport.trackAddCartBtnClick(context, rowsBean, tvSpellGroupPopsubmit?.text?.toString(), 4)
            checkoutGotoSettle(rowsBean)
            trackClick("2")
            btnClick(tvSpellGroupPopsubmit?.text?.toString())
        }
        rtvSpellGroupRightNow?.setOnClickListener {
            AddCartPopupWindowReport.trackAddCartBtnClick(context, rowsBean, rtvSpellGroupRightNow?.text?.toString(), 4)
            registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_RIGHT_NOW)
            checkoutGotoSettle(rowsBean)
            trackClick("3")
            btnClick(rtvSpellGroupRightNow?.text?.toString())
        }
        rtvSpellGroupRecommendGoods?.setOnClickListener {
            AddCartPopupWindowReport.trackAddCartBtnClick(context, rowsBean, rtvSpellGroupRecommendGoods?.text?.toString(), 5)
            if (rowsBean.isCanAddToCart) {
                addShopCart(rowsBean.id, tvNumber?.text?.toString()?.trim() ?: "");
            } else {
                registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_SELECT_GOODS)
                checkoutGotoSettle(rowsBean)
                trackClick("4")
            }

            btnClick(rtvSpellGroupRecommendGoods?.text?.toString())
        }
    }

    private fun btnClick(btnName:String?){
        context.jgTrackProductDetailNewBtnClick(
                url = jgTrackBean?.url,
                pageId = jgTrackBean?.pageId,
                title  = jgTrackBean?.title,
                btnName = btnName?:"",
                btnDesc = "底部弹窗",
                jgReferrer = jgTrackBean?.jgReferrer,
                rank = jgTrackBean?.rank?:1,//
                productId =jgTrackBean?.productId?:"",
                productType =jgTrackBean?.productType?:"",
                operationId =jgTrackBean?.mJgOperationPositionInfo?.operationId?:"",
                operationRank =jgTrackBean?.mJgOperationPositionInfo?.operationRank?:1,
                module =jgTrackBean?.module?:"",
                navigation1 =jgTrackBean?.navigation_1?:"",
                navigation2 = jgTrackBean?.navigation_2?:"")

        popWindowButtonClickJGTrack(btnName,tvNumber?.text.toString().trim().toIntOrNull())
    }

    private fun trackClick(buttonType: String) {
        XyyIoUtil.track(
                "pintuan_newFloat_Exposure_buttonClick", hashMapOf(
                "buttonType" to buttonType,
                "productId" to "${rowsBean.id}",
                "spid" to (mFlowData?.spId ?: ""),
                "sptype" to (mFlowData?.spType ?: ""),
                "productType" to (if (mIsWholeSale) "5" else "3")
        )
        )
    }

    private fun popWindowButtonClickJGTrack(btnName:String? = "",number: Int?) {
        val pdBean = ReportPDButtonClick()
        val jgExtendOuterBean = ReportPDExtendOuterBean().apply {
            jgPageListCommonBean?.let {
                sptype = it.sptype ?: ""
                jgspid = it.jgspid ?: ""
                sid = it.sid ?: ""
                resultCnt = it.result_cnt
                pageNo = it.page_no
                pageSize = it.page_size
                totalPage = it.total_page
                rank = jgTrackBean?.rank ?: 1
                keyWord = it.key_word
                listPositionType = rowsBean.positionType.toString()
                listPositionTypename = rowsBean.positionTypeName
                searchSortStrategyId = rowsBean.searchSortStrategyCode
                operationId = rowsBean.operationId
                operationRank = jgTrackBean?.mJgOperationPositionInfo?.operationRank
            }
        }
        pdBean.url = jgTrackBean?.url
        pdBean.referrer = jgTrackBean?.jgReferrer
        pdBean.title = jgTrackBean?.title
        pdBean.outerBean = jgExtendOuterBean
        pdBean.productId = rowsBean.id.toInt()
        pdBean.productName = rowsBean.showName
        pdBean.productFirst = rowsBean.categoryFirstId
        pdBean.productPrice = rowsBean.jgProductPrice
        pdBean.productType = rowsBean.productType.toString()
        pdBean.productShopCode = rowsBean.shopCode
        pdBean.productShopName = rowsBean.shopName
        pdBean.productNumber = number
        pdBean.productActivityType = rowsBean.productActivityType
        pdBean.btnName = btnName
        pdBean.btnDesc = "列表页底部弹窗"
        pdBean.direct = "1"
        ReportManager.getInstance().report(pdBean)
    }

    private fun setBasicInformation(rowsBean: RowsBean, actPtBean: ActPtBean) {
        if (rowsBean == null) {
            return
        }
        groupSpellGroupPrice?.visibility = if (actPtBean.isStepPrice()) View.VISIBLE else View.GONE
        val spec = "规格:" + rowsBean.spec
        val availableQty =
                "库存:" + if (rowsBean.availableQty > 100) "大于100" else "" + rowsBean.availableQty
        val skuStartNum = actPtBean.skuStartNum
        val skuStartNumStr =
            "${actPtBean.skuStartNum}${rowsBean.productUnit}${if (mIsWholeSale) "起购" else "起拼"}"
        val effectStr = "近/远效期：" + rowsBean.nearEffect + "/" + rowsBean.farEffect
        val isValidity =
            TextUtils.isEmpty(rowsBean.nearEffect) || "-" == rowsBean.nearEffect || TextUtils.isEmpty(
                rowsBean.farEffect
            ) || "-" == rowsBean.farEffect

        //药品名称
        tvSpellGroupPopName?.text = rowsBean.showName
        //规格
        tvSpellGroupPopSpec?.text = spec
        //库存
        tvSpellGroupPopInventory?.text = availableQty
        //效期 近效期：nearEffect  远效期：farEffect
        if (isValidity) {
            tvSpellGroupPopEffective?.text = "-"
        } else {
            tvSpellGroupPopEffective?.text = effectStr
        }
        //起拼数量
        tvSpellGroupPopInitial?.text = skuStartNumStr
        tvNumber?.text = skuStartNum.toString()

        try {
            changeNumber(
                tvNumber?.text.toString().trim().toInt(),
                rowsBean.id,
                Integer.parseInt(actPtBean.marketingId),
                handler, true
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
        if (rowsBean.isCanAddToCart) {
            tvSpellGroupPopsubmit?.visibility = View.GONE
            clSupportSuixinpin?.visibility = View.VISIBLE
            rtvSpellGroupRightNow?.text = if (mIsWholeSale) "去抢购" else "立即参团"
        } else {
            tvSpellGroupPopsubmit?.visibility =
                if (actPtBean.supportSuiXinPin) View.GONE else View.VISIBLE
            clSupportSuixinpin?.visibility = if (actPtBean.supportSuiXinPin) View.VISIBLE else View.GONE
            rtvSpellGroupRightNow?.text = if (mIsWholeSale) "去抢购" else "立即参团"
        }
        if (!actPtBean.suiXinPinButtonText.isNullOrEmpty()) {
            rtvSpellGroupRecommendGoods?.text = actPtBean.suiXinPinButtonText
        }
        if (!actPtBean.suiXinPinButtonBubbleText.isNullOrEmpty()) {
            tvSuiXinPinBubble?.visibility = View.VISIBLE
            tvSuiXinPinBubble?.text = actPtBean.suiXinPinButtonBubbleText
        }
        if (rowsBean.isCanAddToCart) {
            rtvSpellGroupRecommendGoods?.text = "加入购物车"
            tvSuiXinPinBubble?.visibility = View.GONE
        }
        if (rowsBean.freeShippingFlag && !TextUtils.isEmpty(rowsBean.freeShippingText)) {
            tvTips?.visibility = View.VISIBLE
            tvTips?.text = rowsBean.freeShippingText
        }
        val buttonList = if ((rowsBean.actPt != null && rowsBean.actPt.supportSuiXinPin) || (rowsBean.actPgby != null && rowsBean.actPgby.supportSuiXinPin)) {
            "{0,1,3,4}"
        } else {
            "{0,1,2}"
        }
        XyyIoUtil.track(
            "pintuan_newFloat_Exposure", hashMapOf(
                "productId" to "${rowsBean.id}",
                "spid" to (mFlowData?.spId ?: ""),
                "sptype" to (mFlowData?.spType ?: ""),
                "buttonList" to buttonList
            )
        )
    }

    private fun getRefreshParams(rowsBean: RowsBean): RequestParams {
        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams()
        params.put("merchantId", merchantId)
        params.put("skuId", rowsBean.id.toString())
        params.put("productNum", tvNumber?.text.toString().trim())
        return params
    }

    /**
     * 校验订单是否可以跳转到待确认订单页
     */
    private fun checkoutGotoSettle(rowsBean: RowsBean) {
        if (rowsBean == null) {
            return
        }
        val isSupportSuiXinPin =
            rowsBean.actPt?.supportSuiXinPin == true || rowsBean.actPgby?.supportSuiXinPin == true

        HttpManager.getInstance()
            .post(
                AppNetConfig.ORDER_V1_GROUPPURCHASEPRESETTLE,
                getRefreshParams(rowsBean),
                object : BaseResponse<SettleBean?>() {
                    override fun onSuccess(
                        content: String?,
                        data: BaseBean<SettleBean?>?,
                        bean: SettleBean?
                    ) {
                        if (data != null) {
                            if (data.isSuccess) {
                                val checkOutIntent =
                                    Intent(context, PaymentActivity::class.java).apply {
                                        putExtra("tranNo", bean?.tranNo)
                                        putExtra("skuId", rowsBean.id.toString())
                                        putExtra(
                                            "productNum",
                                            tvNumber?.text?.toString()?.trim() ?: ""
                                        )
                                        if (mIsWholeSale) {
                                            putExtra("isPgby", "1")
                                        }
                                        putExtra("spType", mFlowData?.spType ?: "0")
                                        putExtra("spId", mFlowData?.spId ?: "")
                                        putExtra("sId", mFlowData?.sId ?: "")
                                        putExtra("isFromList", "1")
                                        putExtra("isSupportOldSxp", if (isSupportSuiXinPin) "1" else "0")
                                        putExtra("shopCode",rowsBean.shopCode?:"")
                                        jgTrackBean?.let { bean->
                                            bean.entrance?.let {
                                                putExtra(IntentCanst.JG_ENTRANCE, it)
                                            }
                                            bean.activityEntrance?.let {
                                                putExtra(IntentCanst.ACTIVITY_ENTRANCE, it)
                                            }
                                        }
                                    }

                                if (bean!!.isShowDialog == 1) {
                                    AlertDialogEx(context)
                                        .setMessage("您的资质已过期，请及时更新，以免影响发货")
                                        .setCanceledOnTouchOutside(false)
                                        .setConfirmButton(
                                            "我知道了"
                                        ) { _: AlertDialogEx?, _: Int ->
                                            if (isSupportSuiXinPin) {
                                                RoutersUtils.open(getJumpUrl(rowsBean, bean))
                                            } else {
                                                context.startActivity(checkOutIntent)
                                            }
                                        }.show()
                                } else {
                                    if (isSupportSuiXinPin) {
                                        RoutersUtils.open(getJumpUrl(rowsBean, bean))
                                    } else {
                                        context.startActivity(checkOutIntent)
                                    }
                                }
                                dismiss()
                            }
                        }
                    }
                })
    }

    /**
     * 获取跳转路由
     * @param productDetail
     * @param bean
     */
    private fun getJumpUrl(rowsBean: RowsBean, bean: SettleBean): String? {
        if (context is ComponentActivity) {
            val viewModel = ViewModelProvider(
                get().getGlobalViewModelStore(),
                SavedStateViewModelFactory(
                    context.application,
                    context
                )
            ).get(SpellGroupRecommendGoodsViewModel::class.java)
            //如果支持随心拼则添加主品
            val params: MutableMap<String, String> = hashMapOf()
            //拼团
            if (rowsBean.actPt != null && rowsBean.actPt.supportSuiXinPin && mCartDataBean != null) {
                val mainCartGoodsInfo = SpellGroupGoodsItem()
                mainCartGoodsInfo.goodsSelectedCount = mCartDataBean!!.qty
                viewModel.mainGoodsCount = mCartDataBean!!.qty.toString() + ""
                mainCartGoodsInfo.skuId = rowsBean.id.toString() + ""
                mainCartGoodsInfo.goodsUrl = rowsBean.imageUrl
                mainCartGoodsInfo.goodsTitle = rowsBean.showName
                mainCartGoodsInfo.goodsUnit = rowsBean.productUnit
                mainCartGoodsInfo.goodsPrice = mCartDataBean!!.price
                mainCartGoodsInfo.totalPrice = mCartDataBean!!.totalAmount
                val spellGroupRecommendGoodsBean = SpellGroupRecommendGoodsBean(
                    ArrayList(),
                    mainCartGoodsInfo,
                    HashMap(),
                    CartGoodsInfo(),
                    false
                )
                viewModel.updateData(spellGroupRecommendGoodsBean, false)
            }
            params["isSupportOldSxp"] = "1"
            params["tranNo"] = bean.tranNo
            params["skuId"] = rowsBean.id.toString()
            params["productNum"] = tvNumber!!.text.toString().trim { it <= ' ' }
            if (mIsWholeSale) {
                params["isPgby"] = "1"
            }
            params["isFromList"] = "1"
            if (mFlowData != null) {
                if (mFlowData!!.spType != null) {
                    params["spType"] = mFlowData!!.spType ?: ""
                }
                if (mFlowData!!.spId != null) {
                    params["spId"] = mFlowData!!.spId ?: ""
                }
                if (mFlowData!!.sId != null) {
                    params["sId"] = mFlowData!!.sId ?: ""
                }
            }

            jgTrackBean?.let { bean ->
                bean.entrance?.let {
                    params[IntentCanst.JG_ENTRANCE] = it
                }
                bean.activityEntrance?.let {
                    params[IntentCanst.ACTIVITY_ENTRANCE] = it
                }
            }
            return viewModel.getJumpRouter(CURRENT_PAGE_DEFAULT, params)
        }
        return null
    }

    /*
     * 数量加减
     * */
    private fun bindNumAddOrSub(rowsBean: RowsBean, actPtBean: ActPtBean?) {
        if (rowsBean == null || actPtBean == null) {
            return
        }
        val skuId = rowsBean.id
        val marketingId = Integer.parseInt(actPtBean.marketingId)
        var skuStartNum = actPtBean.skuStartNum
        val split = rowsBean.isSplit
        val isSplit = split == 1 //是否可拆零 0:不可拆零；1:可拆零 默认1
        if (skuStartNum <= 1) {
            skuStartNum = 1
        }

        // 商品不可拆零：按照商品的中包装数量累加和递减；
        // 商品可拆零：按照商品中包装数量累加，按照1递减；
        // 如果已经等于起拼数量，则不可再减

        // 起拼数量
        finalSkuStartNum = skuStartNum
        // 中包装数量
        val mediumPackageNum = rowsBean.mediumPackageNum
        ivNumSub?.setOnClickListener {
            val num = tvNumber!!.text.toString().trim()
            if (TextUtils.isEmpty(num) || num.length > 4) {
                return@setOnClickListener
            }
            numOnClick(num, mediumPackageNum, skuId, marketingId, isSplit, false, handler)
            trackClick("1")
        }
        ivNumAdd?.setOnClickListener {
            val num = tvNumber?.text.toString().trim()
            if (TextUtils.isEmpty(num) || num.length > 4) {
                return@setOnClickListener
            }
            numOnClick(num, mediumPackageNum, skuId, marketingId, isSplit, true, handler)
            trackClick("0")
        }
        //编辑弹出对话框加减数量
        tvNumber?.setOnClickListener { v: View ->
            val num = tvNumber?.text.toString().trim()
            if (context is BaseActivity) {
                context.hideSoftInput()
            }
            DialogUtil.addOrSubDialog(
                v.context as BaseActivity,
                InputType.TYPE_CLASS_NUMBER,
                num,
                mediumPackageNum,
                isSplit,
                true,
                object : DialogClickListener {
                    private var mImm: InputMethodManager? = null
                    override fun confirm(content: String) {
                        var shopNum: Int
                        shopNum = try {
                            Integer.valueOf(content)
                        } catch (ex: Exception) {
                            0
                        }
                        if (shopNum < finalSkuStartNum) {
                            shopNum = finalSkuStartNum
                        }
                        changeNumber(shopNum, skuId, marketingId, handler)
                    }

                    override fun cancel() {}
                    override fun showSoftInput(view: View) {
                        try {
                            if (mImm == null) {
                                mImm =
                                    view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                            }
                            mImm?.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
                        } catch (e: Throwable) {
                            e.printStackTrace()
                        }
                    }
                })
        }
    }

    /*
     * 加减编辑购买数量
     * */
    private fun numOnClick(
        num: String,
        mediumPackageNum: Int,
        skuId: Long,
        marketingId: Int,
        isSplit: Boolean,
        isAdd: Boolean,
        handler: Handler
    ) {

        //获取商品的数量
        var shopNum: Int
        shopNum = try {
            Integer.valueOf(num)
        } catch (ex: Exception) {
            0
        }
        if (!isAdd) {
            if (isSplit) {
                shopNum--
            } else {
                shopNum -= mediumPackageNum
            }
            if (shopNum < finalSkuStartNum) {
                shopNum = finalSkuStartNum
            }
        } else {
            shopNum += mediumPackageNum
            if (shopNum > 99999) {
                shopNum = 99999
            }
        }
        changeNumber(shopNum, skuId, marketingId, handler)
    }

    private fun changeNumber(amount: Int, skuId: Long, promoId: Int, handler: Handler,isFirstChange:Boolean=false) {
        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams()
        val jgRequestParams = JgRequestParams()
        params.put("merchantId", merchantId)
        params.put("amount", amount.toString())
        params.put("skuId", skuId.toString())
        params.put("promoId", promoId.toString())
        params.put("scenceType", if (mIsWholeSale) "1" else "0") //1：批购包邮，0：非批购包邮
        if (!rowsBean.searchSortStrategyCode.isNullOrEmpty()) {
            //搜索策略编码
            params.put("searchSortStrategyCode", rowsBean.searchSortStrategyCode)
        }
        if (!rowsBean.directModule.isNullOrEmpty()) {
            //所属模块 1-搜索列表信息流 2-搜索运营位模块
            params.put("directModule", rowsBean.directModule ?: "")
        }
        rowsBean.jgTrackBean?.let { bean ->

            bean.entrance?.let { //                params.put("entrance", it)
                jgRequestParams.entrance = it
            }
            bean.activityEntrance?.let { //                params.put("activityEntrance", it)
                jgRequestParams.activity_entrance = it
            }
        }
        params.put("sptype", mFlowData?.spType?: "")
        params.put("spid", mFlowData?.spId?: "")
        params.put("sid", mFlowData?.sId?: "")
        jgRequestParams.search_sort_strategy_id = JGTrackManager.getSuperProperty(
                YBMAppLike.getAppContext(),
                JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) as String?
        if (mJgOperationInfo != null) {
            val mJgOperationInfo = mJgOperationInfo!!
            if (!mJgOperationInfo.productId.isNullOrEmpty() && mJgOperationInfo.productId == skuId.toString()) {
                if (mJgOperationInfo.operationId != null) {
//                                        params.put(
//                                                "operationId",
//                                                mJgOperationInfo.operationId)
                    jgRequestParams.operation_id = mJgOperationInfo.operationId
                }
                if (mJgOperationInfo.operationRank != null) {
//                                        params.put(
//                                                "operationRank",
//                                                mJgOperationInfo.operationRank.toString())
                    jgRequestParams.operation_rank = mJgOperationInfo.operationRank
                }
                if (mJgOperationInfo.rank != null) {
//                    params.put(
//                            "rank",
//                            mJgOperationInfo.rank.toString())
                    jgRequestParams.rank = mJgOperationInfo.rank
                }
            }
        }
        mJgSearchRowsBean?.let {
            if ((it.productId ?: "") == rowsBean.id.toString()) {
                jgRequestParams.key_word = it.searchKeyword
                jgRequestParams.list_position_type = it.positionType.toString()
                jgRequestParams.list_position_typename = it.positionTypeName
                jgRequestParams.product_id = it.productId
                jgRequestParams.product_name = it.productName
                jgRequestParams.product_first = it.categoryFirstId
                jgRequestParams.product_number = it.productNumber
                jgRequestParams.product_price = it.jgProductPrice
                jgRequestParams.product_type = it.productType.toString()
                jgRequestParams.product_activity_type = it.productActivityType
                jgRequestParams.product_shop_code = it.shopCode
                jgRequestParams.product_shop_name = it.shopName
                JGTrackManager.GlobalVariable.mJgSearchSomeField?.let {field->
                    field.mJgPageListCommonBean?.let {
                        jgRequestParams.sptype = it.sptype?:""
                        jgRequestParams.jgspid = it.jgspid?:""
                        jgRequestParams.sid = it.sid?:""
                        jgRequestParams.direct = "1"
                        jgRequestParams.page_no = it.page_no?:1
                        jgRequestParams.result_cnt = it.result_cnt?:0
                        jgRequestParams.page_size = it.page_size?:1
                        jgRequestParams.total_page = it.total_page

                    }
                    jgRequestParams.rank = field.rank?:1
                }
            }
        }
        jgRequestParams.session_id = TrackManager.getSessionId(YBMAppLike.getAppContext())
        params.put(
                "mddata",
                Gson().toJson(jgRequestParams))
        if (jgTrackBean?.entrance?.contains(JGTrackManager.TrackShoppingCart.TITLE) == true){ //购物车只传个direct = "3"
            params.paramsMap.remove("mddata")
        }

        HttpManager.getInstance().post(
                AppNetConfig.CHANGECARTFORPROMOTION,
                params,
                object : BaseResponse<CartDataBean?>() {
                    override fun onSuccess(
                            content: String?, obj: BaseBean<CartDataBean?>?,
                            baseBean: CartDataBean?) {
                        if (null != obj) {
                            if (obj.isSuccess) {
                                if (!mIsWholeSale && isFirstChange && !TextUtils.isEmpty(baseBean?.actPurchaseTip)) {
                                    tvLimited?.visibility = View.VISIBLE
                                    tvLimited?.text = baseBean?.actPurchaseTip
                                }
                                mCartDataBean = baseBean
                                handler.sendMessage(
                                        handler.obtainMessage(
                                                CHANGE_SPELL_GROUP_SUCESS,
                                                baseBean))
                                if (!TextUtils.isEmpty(baseBean?.price)) {
                                    tvSpellGroupPrice?.text = baseBean?.price
                            }
                        }
                    }
                }
            })
    }

    @SuppressLint("HandlerLeak")
    val handler: Handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == CHANGE_SPELL_GROUP_SUCESS) {
                if (msg.obj is CartDataBean) {
                    val bean = msg.obj as CartDataBean
                    val numStr = bean.qty.toString()
                    tvNumber?.text = numStr
                    var totalAmount: SpannableString? = SpannableString("")
                    if (!TextUtils.isEmpty(bean.totalAmount)) {
                        totalAmount = StringUtil.setDotAfterSize(
                            "¥" + UiUtils.transform(bean.totalAmount),
                            15,
                            R.color.color_ff2121
                        )
                    }
                    tvTotalAmount?.text = totalAmount
                }
            }
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.show_spell_group_pop
    }

    override fun initView() {
        val lp = LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            0f
        )
        layoutParams = lp
        tvSpellGroupPopName = getView(R.id.tv_spell_group_pop_name)
        tvSpellGroupPopSpec = getView(R.id.tv_spell_group_pop_spec)
        tvSpellGroupPopInventory = getView(R.id.tv_spell_group_pop_inventory)
        tvSpellGroupPopEffective = getView(R.id.tv_spell_group_pop_effective)
        tvSpellGroupPopInitial = getView(R.id.tv_spell_group_pop_initial)
        tvSpellGroupPopsubmit = getView(R.id.tv_spell_group_pop_submit)
        clSupportSuixinpin = getView(R.id.clSupportSuixinpin)
        rtvSpellGroupRightNow = getView(R.id.rtvSpellGroupRightNow)
        rtvSpellGroupRecommendGoods = getView(R.id.rtvSpellGroupRecommendGoods)
        tvSuiXinPinBubble = getView(R.id.tvSuiXinPinBubble)
        tvTotalAmount = getView(R.id.tv_total_amount)
        tvLimited = getView(R.id.tv_limited)
        tvNumber = getView(R.id.tv_number)
        ivNumSub = getView(R.id.iv_numSub)
        ivNumAdd = getView(R.id.iv_numAdd)
        llDiscount = getView(R.id.ll_discount)
        groupSpellGroupPrice = getView(R.id.group_spell_group_price)
        tvSpellGroupPrice = getView(R.id.tv_spell_group_price)
        mPromoList = getView(R.id.promo_list)

        mPromotionTagAdapter = PromotionTagAdapter(R.layout.item_spell_group_pop)
        mPromoList?.adapter = mPromotionTagAdapter
        val ll = LinearLayoutManager(context)
        ll.orientation = LinearLayoutManager.VERTICAL
        mPromoList?.layoutManager = ll
        tvTips = getView(R.id.tvTips)
    }

    companion object {
        private const val CHANGE_SPELL_GROUP_SUCESS = 10
    }

    init {
        this.context = context
        setData(rowsBean, actPtBean)
    }

    override fun onItemClick(skuId: String?) {
    }

    override fun onItemExposure(skuId: String?) {
    }

    private fun loadCSUInfo() {
        if (mRowsBean == null) return
        val params: RequestParams = RequestParams.newBuilder().url(AppNetConfig.PROM_INFO_URL)
            .addParam("csuId", mRowsBean!!.id.toString() + "").build()
        HttpManager.getInstance().post(params, object : BaseResponse<SpellGroupPromoBean>() {

            override fun onSuccess(
                content: String?,
                obj: BaseBean<SpellGroupPromoBean>?,
                spellGroupPromoBean: SpellGroupPromoBean?
            ) {
                super.onSuccess(content, obj, spellGroupPromoBean)
                mPromotionTagAdapter!!.setNewData(spellGroupPromoBean?.tagList)
                if (spellGroupPromoBean?.tagList != null && spellGroupPromoBean.tagList.isEmpty()) {
                    llDiscount!!.visibility = View.GONE
                } else {
                    llDiscount!!.visibility = View.VISIBLE
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
            }

        })
    }

    override fun show(token: View?) {
        super.show(token)
//        rtvSpellGroupRightNow?.isVisible = !hideSpellGroupRightNow
        loadCSUInfo()

        tvSpellGroupPopsubmit?.btnExposure()
        if (clSupportSuixinpin?.isVisible == true) {
            rtvSpellGroupRightNow?.btnExposure()
            rtvSpellGroupRecommendGoods?.btnExposure()
        }
    }

    //按钮曝光
    private fun TextView.btnExposure(){
    }


    private inner class PromotionTagAdapter(layoutResId: Int) :
        BaseQuickAdapter<TagBean, PromotionTagHolder>(layoutResId) {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PromotionTagHolder {
            val holder = super.onCreateViewHolder(parent, viewType)
            holder.mCSUListview.setItemEventListener(this@SpellGroupPopWindow)
            holder.mCSUListview.setIsMainProductVirtualSupplier(rowsBean.isVirtualSupplier)
            return holder
        }

        override fun convert(promotionTagHolder: PromotionTagHolder, tagBean: TagBean) {
            promotionTagHolder.mTitleText.tagStyle(tagBean)
            promotionTagHolder.mDetailText.text = tagBean.description
            if (tagBean.csuList != null && tagBean.csuList.size > 0) {
                promotionTagHolder.mCSUListview.visibility = View.VISIBLE
                promotionTagHolder.mCSUListview.setListData(tagBean.csuList, tagBean, true)
            } else {
                promotionTagHolder.mCSUListview.visibility = View.GONE
            }
        }
    }

    internal class PromotionTagHolder(view: View) : BaseViewHolder(view) {
        val mTitleText: TextView
        val mDetailText: TextView
        val mCSUListview: CSUListView

        init {
            mTitleText = view.findViewById(R.id.title)
            mDetailText = view.findViewById(R.id.desc_title)
            mCSUListview = view.findViewById(R.id.csu_item)
        }
    }

    private fun addShopCart(
        id: Long,
        number: String,
    ) {
        var editShopNumberParams = RequestParams()
        val jgRequestParams = JgRequestParams()
        //服务器同步
        editShopNumberParams.put(
            "merchantId",
            HttpManager.getInstance().merchant_id
        )
        try {
            if (getSuperProperty(
                            this.context,
                            JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) != null) {
                val searchSortStrategyCode = getSuperProperty(
                        this.context,
                        JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) as String?
                //                params.put("searchSortStrategyCode",searchSortStrategyCode);
                jgRequestParams.search_sort_strategy_id = searchSortStrategyCode
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        if (jgTrackBean != null) {
            if (jgTrackBean!!.entrance != null && jgTrackBean!!.entrance!!.isNotEmpty()) {
//                params.put("entrance",jgTrackBean.getEntrance());
                jgRequestParams.entrance = jgTrackBean!!.entrance
            }
            if (jgTrackBean!!.activityEntrance != null && jgTrackBean!!.activityEntrance!!.isNotEmpty()) {
//                params.put("activityEntrance",jgTrackBean.getActivityEntrance());
                jgRequestParams.activity_entrance = jgTrackBean!!.activityEntrance
            }
        }
        mJgOperationInfo?.let { info ->
            if ((info.productId ?: "") == id.toString()) {
                info.operationId?.let {
                    jgRequestParams.operation_id = it
                }
                info.operationRank?.let {
                    jgRequestParams.operation_rank = mJgOperationInfo!!.operationRank
                }
                info.rank?.let {
                    jgRequestParams.rank = it
                }
            }
        }
        mJgSearchRowsBean?.let {
            if ((it.productId ?: "") == rowsBean.id.toString()) {
                jgRequestParams.key_word = it.searchKeyword
                jgRequestParams.list_position_type = it.positionType.toString()
                jgRequestParams.list_position_typename = it.positionTypeName
                jgRequestParams.product_id = it.productId
                jgRequestParams.product_name = it.productName
                jgRequestParams.product_first = it.categoryFirstId
                jgRequestParams.product_number = it.productNumber
                jgRequestParams.product_price = it.jgProductPrice
                jgRequestParams.product_type = it.productType.toString()
                jgRequestParams.product_activity_type = it.productActivityType
                jgRequestParams.product_shop_code = it.shopCode
                jgRequestParams.product_shop_name = it.shopName
                JGTrackManager.GlobalVariable.mJgSearchSomeField?.let {field->
                    field.mJgPageListCommonBean?.let {
                        jgRequestParams.sptype = it.sptype?:""
                        jgRequestParams.jgspid = it.jgspid?:""
                        jgRequestParams.sid = it.sid?:""
                        jgRequestParams.direct = "1"
                        jgRequestParams.page_no = it.page_no?:1
                        jgRequestParams.result_cnt = it.result_cnt?:0
                        jgRequestParams.page_size = it.page_size?:1
                        jgRequestParams.total_page = it.total_page
                    }
                    jgRequestParams.rank = field.rank?:1
                }
            }
        }

        jgRequestParams.product_number = number.toIntOrNull()
        jgRequestParams.session_id = TrackManager.getSessionId(YBMAppLike.getAppContext())
        editShopNumberParams.put(
                "mddata",
                Gson().toJson(jgRequestParams))
        if (jgTrackBean?.entrance?.contains(JGTrackManager.TrackShoppingCart.TITLE) == true){ //购物车只传个direct = "3"
            editShopNumberParams.paramsMap.remove("mddata")
        }

        editShopNumberParams.url = AppNetConfig.BUY_COMMODITY

        editShopNumberParams.put(
            "skuId",
            id.toString() + ""
        )
        editShopNumberParams.put("amount", number)
        editShopNumberParams.put("addType", "3")
        mFlowData?.let {
            editShopNumberParams.put("spType", it.spType)
            editShopNumberParams.put("spId", it.spId)
            editShopNumberParams.put("sId", it.sId)
        }

        //spm加购埋点
        val qtData = getChangeCartParams(context, rowsBean)
        if (qtData != null) {
            editShopNumberParams.put("qtdata", qtData)
        }
        HttpManager.getInstance().post(
            editShopNumberParams,
            object : BaseResponse<CartDataBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<CartDataBean?>?,
                    t: CartDataBean?
                ) {
                    if (obj != null && obj.isSuccess) {
                        //数据库更新
                        ToastUtils.showShort("加入购物车成功")
                        reportAddToCart(
                                AddToCart(
                                        url = jgTrackBean?.url?:"",
                                        title = jgTrackBean?.title?:"",
                                        referrer = jgTrackBean?.jgReferrer?:"",
                                        jGPageListCommonBean = jgPageListCommonBean,
                                        search_sort_strategy_id = rowsBean.searchSortStrategyCode?:"",
                                        rank = jgTrackBean?.mJgOperationPositionInfo?.rank,
                                        operation_id = jgTrackBean?.mJgOperationPositionInfo?.operationId?:"",
                                        operation_rank = jgTrackBean?.mJgOperationPositionInfo?.operationRank,
                                        list_position_type = rowsBean.positionType.toString(),
                                        list_position_typename = rowsBean.positionTypeName?:"",
                                        product_id = rowsBean.id,
                                        product_name = rowsBean.productName?:"",
                                        product_first = rowsBean.categoryFirstId,
                                        product_price = rowsBean.jgProductPrice,
                                        product_type = rowsBean.productType.toString(),
                                        direct = "1",
                                        product_number = number.toIntOrNull(),
                                        product_activity_type = rowsBean.productActivityType,
                                        product_shop_code = rowsBean.shopCode,
                                        product_shop_name = rowsBean.shopName,
                                )
                        )
                        HandlerGoodsDao.getInstance().updateItem(id, 1, false)

//                    ToastUtils.showShort("成功加入采购单");
                        //通知页面去更新购物车的数量
                        LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_SHOPNUMBER))
                        LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_ADD_PRODUCT))

                        dismiss()
                    }
                }

                override fun onFailure(error: NetError) {
                    super.onFailure(error)
                }
            })
    }
}