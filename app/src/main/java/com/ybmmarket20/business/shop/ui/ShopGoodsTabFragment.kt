package com.ybmmarket20.business.shop.ui

import android.annotation.SuppressLint
import android.view.View
import android.view.View.VISIBLE
import androidx.recyclerview.widget.LinearLayoutManager
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchAggsBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.bean.ShopHomeIndexBean.Floor
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.report.coupon.CouponEntryType
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.adapter.GoodsListAdapterNewCategory
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.cbCanUseCoupon
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.rg_property
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.rv_floor_index
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.rv_goodslist
import kotlinx.android.synthetic.main.fragment_shop_home.smartrefresh

class ShopGoodsTabFragment : BaseFragment(), ICouponEntryType {

    var orgId: String? = null
    var shopCode: String? = null
    var floorId: String? = null
    var floorName:String? = null
        set(value) {
            field = value
            jgTrackBean?.navigation_1 = "商品"
            jgTrackBean?.navigation_2 = value
        }
//    var floorType: String? = null

    private var property: String = "smsr.sale_num"

    var floorData: MutableList<Floor> = mutableListOf()
    var floorAdapter: FloorAdapter? = null
    var goodsData: MutableList<RowsBean> = mutableListOf()
    private var goodsListAdapter: GoodListAdapterNew? = null
    var isFloorSelectAll: Boolean = false
    private var mAnchorCsuId: String? = null
    private var jgTrackBean:JgTrackBean? = null
    private var source: String? = ""
    private var isFilterUnableAddCart: String? = ""
    @SuppressLint("NotifyDataSetChanged")
    override fun initData(content: String?) {

        orgId = arguments?.getString("orgId")
        shopCode = arguments?.getString("shopCode")
        mAnchorCsuId = arguments?.getString("anchorCsuId")
        source = arguments?.getString("source")
        isFilterUnableAddCart = arguments?.getString("isFilterUnableAddCart")
        jgTrackBean = arguments?.getSerializable(IntentCanst.JG_TRACK_BEAN)?.let { it as? JgTrackBean? }?.apply {
            url = <EMAIL>()
            jgReferrer = <EMAIL>()
            title = JGTrackManager.TrackShopMain.TITLE
            pageId = JGTrackManager.TrackShopMain.PAGE_ID
            module = JGTrackManager.Common.MODULE_PRODUCT_LIST
        }
        floorAdapter = FloorAdapter(R.layout.item_shop_floor, floorData)
        rv_floor_index?.adapter = floorAdapter
        rv_floor_index?.layoutManager = LinearLayoutManager(context)
        floorAdapter?.setOnItemClickListener { _, _, position ->
            if (!floorData[position].isSelect) {
                if (floorData[position].floorName.equals("全部")) {
                    cbCanUseCoupon.visibility = VISIBLE
                    isFloorSelectAll = true
                } else {
                    cbCanUseCoupon.visibility = View.GONE
                    isFloorSelectAll = false
                }
                floorData.forEach { it.isSelect = false }

                floorData[position].isSelect = true
                floorId = floorData[position].floorId
                floorName = floorData[position].floorName
                //floorType = floorData.get(position).floorType
                floorAdapter?.notifyDataSetChanged()
                // 获取对应楼层的商品信息
                getNewGoodlist()
            }
        }

        cbCanUseCoupon.setOnCheckedChangeListener { _, isCheck ->
            isFloorSelectAll = isCheck
            showProgress()
            getNewGoodlist()
        }

        goodsListAdapter = GoodsListAdapterNewCategory(this, goodsData,isAddCartShowPopupWindow = isFromShopCart()).apply {
            setEmptyView(notNullActivity, R.layout.layout_empty_view_all_goods, R.drawable.icon_empty, "哇哦，没有找到相关商品")
            isShowShopInfo = false
            showUnderlinePrice = false
            jgTrackBean = <EMAIL>
            mIsFromShopCartGatherOrders = isFromShopCart()

            resourceViewTrackListener = { rowsBean, i,_ ->

                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
            }

            productClickTrackListener = { rowsBean, i,isBtnClick,mContent,number ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
            }
        }
//        goodlistAdapter?.isConfigPreHot = true
//        goodlistAdapter?.isConfigSpellGroupSellOut = true
//        goodlistAdapter?.flowData = mFlowData
        rv_goodslist.itemAnimator = null
        rv_goodslist.adapter = goodsListAdapter
        rv_goodslist.layoutManager = LinearLayoutManager(context)
        goodsListAdapter?.setOnLoadMoreListener({ getLoadMoreGoodsList() }, rv_goodslist)
        smartrefresh?.setOnRefreshListener {
            getNewGoodlist()
        }
        rg_property?.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_01 -> {
                    property = "smsr.sale_num"
                    XyyIoUtil.track("shopHome_Sort_Click", hashMapOf("text" to "综合"))
                }    // 综合排序
                R.id.rb_02 -> {
                    property = "spa.sale_num"
                    XyyIoUtil.track("shopHome_Sort_Click", hashMapOf("text" to "销量"))
                }     // 销量排序
            }
            getNewGoodlist()
        }
        getGoodsIndex()
        XyyIoUtil.track("shopHome_Sort_Click", hashMapOf("text" to "综合"))
    }

    private fun isFromShopCart() = source == "1"

    private fun getGoodsIndex() {
        val aggparams = RequestParams().apply {
            shopCode?.let { put("shopCodes", shopCode) }
            orgId?.let { put("orgId", orgId) }
        }

        HttpManager.getInstance().post(AppNetConfig.SORTNET_aggs, aggparams, object : BaseResponse<SearchAggsBean?>() {
            @SuppressLint("NotifyDataSetChanged")
            override fun onSuccess(content: String?, obj: BaseBean<SearchAggsBean?>?, t: SearchAggsBean?) {
                t?.aggregations?.catStats?.let {
                    floorData.clear()
                    floorData.add(Floor().apply {
                        floorName = "全部"
                    })
                    cbCanUseCoupon?.post {
                        cbCanUseCoupon?.visibility = VISIBLE
                    }
                    isFloorSelectAll = true
                    it.forEach {
                        floorData.add(Floor().apply {
                            floorId = it.key
                            floorName = it.showName
                        })
                    }
                    if (floorData.size > 0) {
                        floorData[0].isSelect = true
                        floorId = floorData[0].floorId
                        floorName = floorData[0].floorName
                        //floorType = floorData.get(0).floorType
                        getNewGoodlist()
                    }

                    floorAdapter?.notifyDataSetChanged()
                }
            }
        })
    }


    /**
     * 请求数据
     */
    private fun getLoadMoreGoodsList() {
        HttpManager.getInstance().post(if (SpUtil.isKa()) AppNetConfig.SORTNET_KA else AppNetConfig.SORTNET,
            getNewGoodlistRequestParams(true), object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(content: String?, obj: BaseBean<SearchResultBean?>?, brandBean: SearchResultBean?) {
                    brandBean?.let { updateGoodsData(false, it) }
                }
            })
    }

    /**
     */
    private fun getNewGoodlist() {
        showProgress()
        val newgoodparams = getNewGoodlistRequestParams(false)
        HttpManager.getInstance()
            .post(if (SpUtil.isKa()) AppNetConfig.SORTNET_KA else AppNetConfig.SORTNET, newgoodparams, object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(content: String?, obj: BaseBean<SearchResultBean?>, brandBean: SearchResultBean?) {
                    dismissProgress()
                    smartrefresh?.finishRefresh()
                    goodsData.clear()
                    brandBean?.let {
                        updateGoodsData(true, it)
                    }
                }

                override fun onFailure(error: NetError) {
                    dismissProgress()
                    smartrefresh.finishRefresh()
                }
            })
    }

    private var searchMoreParams: RequestParams? = null

    /**
     *  更新商品信息
     */
    private fun updateGoodsData(isRefresh: Boolean, rowsBeans: SearchResultBean) {
        goodsListAdapter?.flowData = FlowData(rowsBeans.sptype, rowsBeans.spid, rowsBeans.sid, "", "", null)
        searchMoreParams = rowsBeans.requestParams
        rowsBeans.rows?.let {
            AdapterUtils.addLocalTimeForRows(rowsBeans.rows)
            goodsListAdapter?.let {
                it.jgTrackBean = jgTrackBean
                AdapterUtils.notifyAndControlLoadmoreStatus(rowsBeans.rows, it, isRefresh, rowsBeans.isEnd)
            }
            // 请求并更新折后价
            goodsListAdapter?.let { AdapterUtils.getAfterDiscountPrice(rowsBeans.rows, it) }
        }
    }

    /**
     * 请求参数
     */
    private fun getNewGoodlistRequestParams(loadMore: Boolean): RequestParams? =
        if (loadMore) searchMoreParams else RequestParams().apply {
            put("merchantId", SpUtil.getMerchantid())
            //isThirdCompany 字段废弃，后端使用orgId和shopCodes判断店铺类型
            orgId?.let {
                put("orgId", orgId)
//                put("isThirdCompany", "1")
            }
            shopCode?.let {
                put("shopCodes", shopCode)
//                put("isThirdCompany", "0")
            }
            floorId?.let { put("categoryFirstId", floorId) }
            put("property", property)
            if (property.endsWith("spa.sale_num")){
                put("direction", "desc")
            }
//            put("sptype", mFlowData.spType)
            put("spFrom", "2")
            put("sptype", "14")
            put("isFilterUnableAddCart", isFilterUnableAddCart)
            if (isFloorSelectAll && cbCanUseCoupon.isChecked) {
                put("isAvailableCoupons", "1")
            }
            //全部选项并且锚点商品的id不为空
            if (!mAnchorCsuId.isNullOrEmpty() && floorId.isNullOrEmpty()) {
                put("anchorCsuId", mAnchorCsuId)
            }
        }


    override fun initTitle() {
    }

    override fun getParams(): RequestParams? = null

    override fun getUrl(): String? = null

    override fun getLayoutId(): Int = R.layout.fragment_shop_goods_tab
    override fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_SHOP_GOODS
}