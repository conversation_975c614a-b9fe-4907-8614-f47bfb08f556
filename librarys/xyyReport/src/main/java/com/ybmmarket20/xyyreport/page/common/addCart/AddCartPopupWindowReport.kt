package com.ybmmarket20.xyyreport.page.common.addCart

import android.content.Context
import com.ybmmarket20.report.ReportActionProductButtonClickBean
import com.ybmmarket20.report.ReportActionSearchProductButtonClickBean
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportActionSubModuleGoodsClickBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.commodity.CommoditySpmConstant
import com.ybmmarket20.xyyreport.page.search.SearchReportConstant
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmExtensionConstant
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

/**
 * ShowSpellGroupPopWindow
 * ListItemAddCartPopWindow
 * SpellGroupPopWindow
 */
object AddCartPopupWindowReport {

    @JvmStatic
    fun trackAddCartBtnClick(context: Context, rowsBeanInfo: IRowsBeanInfo?, content: String?, btnPosition: Int) {
        trackAddCartBtnClick(context, rowsBeanInfo, content, btnPosition, false)
    }

    @JvmStatic
    fun trackAddCartBtnClick(context: Context, rowsBeanInfo: IRowsBeanInfo?, content: String?, btnPosition: Int, mNoAddButtonWindow: Boolean) {
        if (context !is XyyReportActivity) return
        if (rowsBeanInfo?.onOpGoods() == true && !rowsBeanInfo.onOpSingleGoods()) return
        if (context.getScmCnt() == null) return
        if (rowsBeanInfo?.isCart() == true) return
        val spm = (context.getExtensionValue(AddCartPopupWindowConstant.EXTENSION_GOODS_LIST_ITEM_SPM) as? SpmBean)?.newInstance()
        val scm = (context.getExtensionValue(AddCartPopupWindowConstant.EXTENSION_GOODS_LIST_ITEM_SCM) as? ScmBean)?.newInstance()
        val isSearch = context.getExtensionValue(AddCartPopupWindowConstant.EXTENSION_GOODS_LIST_QT_IS_SEARCH) as? Boolean
        val qtListData = rowsBeanInfo?.getQtListData()
        val productId = rowsBeanInfo?.getSpmProductId()
        val productName = rowsBeanInfo?.getSpmProductName()
        val qtSkuData = rowsBeanInfo?.getSpmQtSkuData()
        val isGoods = context.getExtensionValue(AddCartPopupWindowConstant.EXTENSION_GOODS_LIST_QT_IS_GOODS) as? Boolean
        if (mNoAddButtonWindow) return
        val iReport = if (productName != null && isSearch == true) {
            //商品Feed
            SpmLogUtil.print("商品弹窗点击-搜索商品Feed")
            setSpmD(spm, btnPosition)
            setScmD(scm, content)
            SpmUtil.setSpmE(context, spm)
            scm?.apply {
                this.scmE = SpmUtil.createScmE(context.getExtensionValue(SearchReportConstant.EXTENSION_SEARCH_GOODS_SCM_ID)?.toString())
            }
            ReportActionSearchProductButtonClickBean().apply {
                this.productId = productId
                this.productName = productName
                this.qtListData = qtListData
                this.qtSkuData = qtSkuData
            }
        } else if(isGoods != null && isGoods) {
            // 保持组件逻辑，但不发送商品按钮点击埋点
            ReportActionSubModuleClickBean()
        } else {
            //组件
            SpmLogUtil.print("商品弹窗点击-商品组件")
            spm?.spmD = "ftFloatProd@Z_btn@$btnPosition"
            scm?.scmD = "text-$content"
            SpmUtil.setSpmE(context, spm)
            SpmUtil.setScmE(scm)
            ReportActionSubModuleClickBean()
        }
        SpmUtil.checkAnalysisContextAndEnable(context) {
            addCartActionTrack(context, TrackData(spm, scm))
            ReportUtil.track(context, iReport, spm, scm)
        }
    }

    fun addExtensionForAddCartPopupWindow(context: Context, spm: SpmBean?, scm: ScmBean?, isSearch: Boolean = false, isGoods: Boolean = false) {
        if (context !is XyyReportActivity) return
        context.putExtension(AddCartPopupWindowConstant.EXTENSION_GOODS_LIST_ITEM_SPM, spm)
        context.putExtension(AddCartPopupWindowConstant.EXTENSION_GOODS_LIST_ITEM_SCM, scm)
        context.putExtension(AddCartPopupWindowConstant.EXTENSION_GOODS_LIST_QT_IS_SEARCH, isSearch)
        context.putExtension(AddCartPopupWindowConstant.EXTENSION_GOODS_LIST_QT_IS_GOODS, isGoods)
    }

    private fun setSpmD(spm: SpmBean?, btnPosition: Int) {
        try {
            val spmD = spm?.spmD
            if (spmD != null) {
                val arr = spmD.split("btn@")
                if (arr.isNotEmpty()) {
                    spm.spmD = "${arr[0]}ftFloatProd@Z_btn@$btnPosition"
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setScmD(scm: ScmBean?, content: String?) {
        try {
            val scmD = scm?.scmD
            if (scmD != null) {
                val arr = scmD.split("-").toMutableList()
                arr[arr.size-1] = content?: ""
                scm.scmD = arr.joinToString("-")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 加购操作添加spm参数
     */
    fun addCartActionTrack(context: Context, trackData: TrackData?) {
        if (context !is XyyReportActivity) return
        context.putExtension(AddCartPopupWindowConstant.EXTENSION_ADD_CART_ACTION_ITEM_SPM, trackData?.spmEntity)
        context.putExtension(AddCartPopupWindowConstant.EXTENSION_ADD_CART_ACTION_ITEM_SCM, trackData?.scmEntity)
    }

}