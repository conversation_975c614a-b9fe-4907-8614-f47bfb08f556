package com.ybmmarket20.bean

import com.ybmmarket20.utils.SpUtil
import com.ydmmarket.report.annotation.ReportEventName
import com.ydmmarket.report.annotation.ReportParamsKey

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/12/24 10:35
 *    desc   :
 */
// 注意：此极光埋点事件已被移除，Bean类保留以保持兼容性
@ReportEventName("app_tab_order_click")
class OrderTabClick {
    @ReportParamsKey("account_id")
    var accountId: String? = SpUtil.getAccountId()

    @ReportParamsKey("merchant_id")
    var merchantId: String? = SpUtil.getMerchantid()
}